<?php
namespace app\controller;

use app\BaseController;
use app\service\RbacService;
use app\util\JwtUtil;
use think\facade\Db;
use think\Request;

/**
 * RBAC权限管理控制器
 */
class Rbac extends BaseController
{
    /**
     * 统一的token验证方法
     */
    private function validateToken(Request $request)
    {
        // 从header获取token - 支持多种header名称
        $token = $request->header('token') ?: $request->header('Authorization');

        // 处理Bearer token格式
        if ($token && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (!$token) {
            return [
                'code' => 401,
                'msg' => '缺少认证token'
            ];
        }

        // 验证token
        $payload = JwtUtil::validateToken($token);
        if (!$payload) {
            return [
                'code' => 401,
                'msg' => 'token无效或已过期'
            ];
        }

        $userId = $payload['user_id'] ?? null;
        if (!$userId) {
            return [
                'code' => 401,
                'msg' => 'token中缺少用户信息'
            ];
        }

        return [
            'code' => 200,
            'user_id' => $userId,
            'payload' => $payload
        ];
    }

    /**
     * 获取用户权限信息
     */
    public function getUserPermissions(Request $request)
    {
        try {
            // 验证token
            $tokenResult = $this->validateToken($request);
            if ($tokenResult['code'] !== 200) {
                return json($tokenResult);
            }

            $userId = $tokenResult['user_id'];

            // 获取用户权限信息
            $permissionInfo = RbacService::getUserDisplayInfo($userId);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $permissionInfo
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取权限信息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 为用户分配角色
     */
    public function assignRole(Request $request)
    {
        try {
            // 验证操作者权限
            $token = $request->header('token');
            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效']);
            }

            $operatorId = $payload['user_id'];
            
            // 检查操作者是否有权限管理权限
            if (!RbacService::hasPermission($operatorId, 'system.permission_manage')) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            // 获取参数
            $userId = $request->param('user_id');
            $roleCode = $request->param('role_code');
            $scopeType = $request->param('scope_type');
            $scopeId = $request->param('scope_id');

            if (!$userId || !$roleCode) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }

            // 分配角色
            $result = RbacService::assignRole($userId, $roleCode, $scopeType, $scopeId, $operatorId);
            
            return json([
                'code' => 200,
                'msg' => '角色分配成功',
                'data' => ['id' => $result]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '分配角色失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 移除用户角色
     */
    public function removeRole(Request $request)
    {
        try {
            // 验证操作者权限
            $token = $request->header('token');
            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效']);
            }

            $operatorId = $payload['user_id'];
            
            // 检查操作者是否有权限管理权限
            if (!RbacService::hasPermission($operatorId, 'system.permission_manage')) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            // 获取参数
            $userId = $request->param('user_id');
            $roleCode = $request->param('role_code');
            $scopeType = $request->param('scope_type');
            $scopeId = $request->param('scope_id');

            if (!$userId || !$roleCode) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }

            // 移除角色
            $result = RbacService::removeRole($userId, $roleCode, $scopeType, $scopeId);
            
            return json([
                'code' => 200,
                'msg' => $result ? '角色移除成功' : '角色不存在或已移除'
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '移除角色失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 检查用户权限
     */
    public function checkPermission(Request $request)
    {
        try {
            // 验证token
            $tokenResult = $this->validateToken($request);
            if ($tokenResult['code'] !== 200) {
                return json($tokenResult);
            }

            $userId = $tokenResult['user_id'];
            $permissionCode = $request->param('permission_code');
            $scopeType = $request->param('scope_type');
            $scopeId = $request->param('scope_id');

            if (!$permissionCode) {
                return json(['code' => 400, 'msg' => '缺少权限代码']);
            }

            // 检查权限
            $hasPermission = RbacService::hasPermission($userId, $permissionCode, $scopeType, $scopeId);

            return json([
                'code' => 200,
                'msg' => '检查完成',
                'data' => [
                    'has_permission' => $hasPermission
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '权限检查失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取所有角色列表
     */
    public function getRoles(Request $request)
    {
        try {
            // 验证token
            $tokenResult = $this->validateToken($request);
            if ($tokenResult['code'] !== 200) {
                return json($tokenResult);
            }

            $roles = Db::name('rbac_roles')
                ->where('status', 1)
                ->order('is_system desc, id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $roles
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取角色列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取所有权限列表
     */
    public function getPermissions(Request $request)
    {
        try {
            // 验证token
            $tokenResult = $this->validateToken($request);
            if ($tokenResult['code'] !== 200) {
                return json($tokenResult);
            }

            $permissions = Db::name('rbac_permissions')
                ->where('status', 1)
                ->order('module, id')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $permissions
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取权限列表失败: ' . $e->getMessage()]);
        }
    }


}
