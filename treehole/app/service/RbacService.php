<?php
namespace app\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * RBAC权限管理服务
 */
class RbacService
{
    /**
     * 获取用户的基础状态
     */
    public static function getUserBaseStatus($userId)
    {
        $user = Db::name('user')->where('id', $userId)->find();
        return $user ? $user['status'] : 'unverified';
    }

    /**
     * 获取用户的所有附加角色
     */
    public static function getUserRoles($userId)
    {
        $cacheKey = "user_roles_{$userId}";
        
        return Cache::remember($cacheKey, function() use ($userId) {
            return Db::name('rbac_user_roles')
                ->alias('ur')
                ->join('rbac_roles r', 'ur.role_id = r.id')
                ->where('ur.user_id', $userId)
                ->where('ur.status', 1)
                ->where(function($query) {
                    $query->whereNull('ur.expires_at')
                          ->whereOr('ur.expires_at', '>', date('Y-m-d H:i:s'));
                })
                ->field('r.code, r.name, ur.scope_type, ur.scope_id')
                ->select()
                ->toArray();
        }, 300); // 缓存5分钟
    }

    /**
     * 获取用户的所有权限
     */
    public static function getUserPermissions($userId)
    {
        $cacheKey = "user_permissions_{$userId}";
        
        return Cache::remember($cacheKey, function() use ($userId) {
            // 获取用户角色对应的权限
            $permissions = Db::name('rbac_user_roles')
                ->alias('ur')
                ->join('rbac_role_permissions rp', 'ur.role_id = rp.role_id')
                ->join('rbac_permissions p', 'rp.permission_id = p.id')
                ->where('ur.user_id', $userId)
                ->where('ur.status', 1)
                ->where('p.status', 1)
                ->where(function($query) {
                    $query->whereNull('ur.expires_at')
                          ->whereOr('ur.expires_at', '>', date('Y-m-d H:i:s'));
                })
                ->field('p.code, p.name, p.module, ur.scope_type, ur.scope_id')
                ->select()
                ->toArray();
            
            return $permissions;
        }, 300);
    }

    /**
     * 检查用户是否有特定权限
     */
    public static function hasPermission($userId, $permissionCode, $scopeType = null, $scopeId = null)
    {
        // 先检查基础状态
        $baseStatus = self::getUserBaseStatus($userId);
        
        // 禁言用户没有任何权限
        if ($baseStatus === '禁言') {
            return false;
        }
        
        // 超级管理员拥有所有权限
        if ($baseStatus === '超级管理员') {
            return true;
        }
        
        // 管理员对其认证学校有权限
        if ($baseStatus === '管理员') {
            $user = Db::name('user')->where('id', $userId)->find();
            $userSchoolId = $user['verified_university_id'] ?? null;
            
            // 如果是学校相关权限，检查学校匹配
            if ($scopeType === 'school' && $scopeId && $userSchoolId) {
                return $scopeId == $userSchoolId;
            }
            
            // 管理员对系统级权限也有部分权限
            $adminPermissions = [
                'user.auth_review', 'content.review', 'content.delete',
                'activity.review', 'user.status_manage', 'content.moderate',
                'group.manage', 'official_account.manage'  // 可以随时添加新权限
            ];
            if (in_array($permissionCode, $adminPermissions)) {
                return true;
            }
        }
        
        // 检查附加角色权限
        $permissions = self::getUserPermissions($userId);
        
        foreach ($permissions as $permission) {
            if ($permission['code'] === $permissionCode) {
                // 如果不需要检查作用域，直接返回true
                if ($scopeType === null) {
                    return true;
                }
                
                // 检查作用域匹配
                if ($permission['scope_type'] === $scopeType && 
                    $permission['scope_id'] == $scopeId) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 检查用户是否有基础功能权限（已认证用户）
     */
    public static function hasBasePermission($userId)
    {
        $baseStatus = self::getUserBaseStatus($userId);
        return in_array($baseStatus, ['verified', '管理员', '超级管理员']);
    }

    /**
     * 检查用户是否是某个群的管理员
     */
    public static function isGroupAdmin($userId, $groupId)
    {
        return self::hasPermission($userId, 'group.manage', 'group', $groupId) ||
               self::hasPermission($userId, 'group.member_manage', 'group', $groupId);
    }

    /**
     * 检查用户是否是某个社团的管理员
     */
    public static function isOrganizationAdmin($userId, $orgId)
    {
        return self::hasPermission($userId, 'organization.manage', 'organization', $orgId);
    }

    /**
     * 为用户分配角色
     */
    public static function assignRole($userId, $roleCode, $scopeType = null, $scopeId = null, $grantedBy = null)
    {
        // 获取角色ID
        $role = Db::name('rbac_roles')->where('code', $roleCode)->find();
        if (!$role) {
            throw new \Exception("角色不存在: {$roleCode}");
        }

        // 检查是否已存在
        $exists = Db::name('rbac_user_roles')
            ->where('user_id', $userId)
            ->where('role_id', $role['id'])
            ->where('scope_type', $scopeType)
            ->where('scope_id', $scopeId)
            ->find();

        if ($exists) {
            // 如果已存在但被禁用，则重新启用
            if ($exists['status'] == 0) {
                Db::name('rbac_user_roles')
                    ->where('id', $exists['id'])
                    ->update(['status' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
            }
            return $exists['id'];
        }

        // 插入新记录
        $data = [
            'user_id' => $userId,
            'role_id' => $role['id'],
            'scope_type' => $scopeType,
            'scope_id' => $scopeId,
            'granted_by' => $grantedBy,
            'status' => 1
        ];

        $id = Db::name('rbac_user_roles')->insertGetId($data);
        
        // 清除缓存
        self::clearUserCache($userId);
        
        return $id;
    }

    /**
     * 移除用户角色
     */
    public static function removeRole($userId, $roleCode, $scopeType = null, $scopeId = null)
    {
        $role = Db::name('rbac_roles')->where('code', $roleCode)->find();
        if (!$role) {
            return false;
        }

        $result = Db::name('rbac_user_roles')
            ->where('user_id', $userId)
            ->where('role_id', $role['id'])
            ->where('scope_type', $scopeType)
            ->where('scope_id', $scopeId)
            ->update(['status' => 0, 'updated_at' => date('Y-m-d H:i:s')]);

        if ($result) {
            self::clearUserCache($userId);
        }

        return $result;
    }

    /**
     * 清除用户权限缓存
     */
    public static function clearUserCache($userId)
    {
        Cache::delete("user_roles_{$userId}");
        Cache::delete("user_permissions_{$userId}");
    }

    /**
     * 获取用户在前端显示的权限信息
     */
    public static function getUserDisplayInfo($userId)
    {
        $baseStatus = self::getUserBaseStatus($userId);
        $roles = self::getUserRoles($userId);
        
        return [
            'base_status' => $baseStatus,
            'roles' => $roles,
            'permissions' => self::getUserPermissions($userId)
        ];
    }


    /**
     * 智能权限检查，返回详细的权限状态和提示信息
     */
    public static function checkPermissionWithReason($userId, $permissionCode, $scopeType = null, $scopeId = null)
    {
        $user = Db::name('user')->where('id', $userId)->find();
        if (!$user) {
            return [
                'has_permission' => false,
                'reason' => 'user_not_found',
                'message' => '用户不存在',
                'action' => null
            ];
        }

        // 先调用现有的权限检查方法
        $hasPermission = self::hasPermission($userId, $permissionCode, $scopeType, $scopeId);

        if ($hasPermission) {
            return [
                'has_permission' => true,
                'reason' => 'success',
                'message' => '权限验证通过'
            ];
        }

        // 权限检查失败，分析原因并给出智能提示
        $baseStatus = self::getUserBaseStatus($userId);

        // 1. 检查是否被禁言
        if ($baseStatus === '禁言') {
            return [
                'has_permission' => false,
                'reason' => 'banned',
                'message' => '您已被禁言，无法进行此操作',
                'action' => 'contact_admin'
            ];
        }

        // 2. 检查是否需要认证
        if ($baseStatus === 'unverified') {
            return [
                'has_permission' => false,
                'reason' => 'need_verification',
                'message' => '请先完成身份认证后再操作',
                'action' => 'verify'
            ];
        }

        // 3. 已认证用户但权限不足
        if (in_array($baseStatus, ['verified', 'temporary_verified'])) {
            return [
                'has_permission' => false,
                'reason' => 'need_role',
                'message' => '权限不足，需要特定角色权限',
                'action' => 'contact_admin'
            ];
        }

        // 4. 管理员权限不足
        if (in_array($baseStatus, ['管理员', '社团管理员', '超级管理员'])) {
            return [
                'has_permission' => false,
                'reason' => 'need_role',
                'message' => '权限不足，需要特定权限',
                'action' => 'contact_admin'
            ];
        }

        return [
            'has_permission' => false,
            'reason' => 'unknown_status',
            'message' => '用户状态异常，请联系管理员',
            'action' => 'contact_admin'
        ];
    }

}
